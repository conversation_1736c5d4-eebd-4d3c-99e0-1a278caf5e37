import { ConfigValidator } from '../validator';

export const performance = {
    enableCaching: process.env.ENABLE_CACHING === 'true',
    cacheTimeout: ConfigValidator.validateNumber(process.env.CACHE_TIMEOUT, 'CACHE_TIMEOUT', 1, 86400, 300),
    maxConcurrentCalls: ConfigValidator.validateNumber(process.env.MAX_CONCURRENT_CALLS, 'MAX_CONCURRENT_CALLS', 1, 1000, 100),
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    // Audio processing performance settings
    audioFastMode: process.env.AUDIO_FAST_MODE === 'true',
    audioNoiseReduction: process.env.AUDIO_NOISE_REDUCTION !== 'false',
    audioCompression: process.env.AUDIO_COMPRESSION !== 'false',
    audioAGC: process.env.AUDIO_AGC !== 'false',
    audioVoiceEnhancement: process.env.AUDIO_VOICE_ENHANCEMENT !== 'false'
};
